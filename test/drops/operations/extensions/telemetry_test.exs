defmodule Drops.Operations.Extensions.TelemetryTest do
  use Drops.OperationCase, async: false

  alias Drops.Operations.Extensions.Telemetry

  defmodule TelemetryTestHandler do
    def handle_event(event, measurements, metadata, test_pid) do
      send(test_pid, {:telemetry_event, event, measurements, metadata})
    end
  end

  describe "enable?/1" do
    test "returns false when telemetry is not configured" do
      refute Telemetry.enable?([])
      refute Telemetry.enable?(telemetry: false)
    end

    test "returns true when telemetry is enabled with boolean" do
      assert Telemetry.enable?(telemetry: true)
    end

    test "returns true when telemetry is configured with options" do
      assert Telemetry.enable?(telemetry: [steps: [:validate, :execute]])
    end

    test "returns true when telemetry is configured with custom identifier" do
      assert Telemetry.enable?(telemetry: [identifier: :my_app])
    end

    test "returns true when telemetry is configured with both identifier and steps" do
      assert Telemetry.enable?(
               telemetry: [identifier: :my_app, steps: [:validate, :execute]]
             )
    end
  end

  describe "default_opts/1" do
    test "returns empty list" do
      assert Telemetry.default_opts([]) == []
    end
  end

  describe "telemetry events" do
    setup do
      # Capture telemetry events
      test_pid = self()

      :telemetry.attach_many(
        "test-telemetry",
        [
          [:drops, :operation, :start],
          [:drops, :operation, :stop],
          [:drops, :operation, :exception],
          [:drops, :operation, :step, :start],
          [:drops, :operation, :step, :stop],
          [:drops, :operation, :step, :exception]
        ],
        &TelemetryTestHandler.handle_event/4,
        test_pid
      )

      on_exit(fn ->
        :telemetry.detach("test-telemetry")
      end)

      :ok
    end

    test "emits start and stop events for operation with default telemetry" do
      defmodule TestOperationDefault do
        use Drops.Operations.Command, telemetry: true

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, Map.put(params, :executed, true)}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:ok, _result} = TestOperationDefault.call(context)

      # Should receive operation start event (using first step name: prepare)
      assert_receive {:telemetry_event, [:drops, :operation, :start], measurements,
                      metadata}

      assert %{system_time: _} = measurements

      assert %{operation: TestOperationDefault, step: :prepare, context: ^context} =
               metadata

      # Should receive operation stop event (using last step name: execute)
      assert_receive {:telemetry_event, [:drops, :operation, :stop], measurements,
                      metadata}

      assert %{duration: _} = measurements

      assert %{operation: TestOperationDefault, step: :execute, context: ^context} =
               metadata
    end

    test "emits events for specific steps when configured" do
      defmodule TestOperationSpecific do
        use Drops.Operations.Command, telemetry: [steps: [:execute]]

        steps do
          def prepare(context), do: {:ok, context}
          def validate(context), do: {:ok, context}

          @impl true
          def execute(%{params: params}) do
            {:ok, Map.put(params, :executed, true)}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:ok, _result} = TestOperationSpecific.call(context)

      # Should only receive events for the execute step
      assert_receive {:telemetry_event, [:drops, :operation, :step, :start], measurements,
                      metadata}

      assert %{system_time: _} = measurements
      assert %{operation: TestOperationSpecific, step: :execute, context: _} = metadata

      assert_receive {:telemetry_event, [:drops, :operation, :step, :stop], measurements,
                      metadata}

      assert %{duration: _} = measurements
      assert %{operation: TestOperationSpecific, step: :execute, context: _} = metadata

      # Should not receive events for prepare or validate steps
      refute_receive {:telemetry_event, [:drops, :operation, :step, :start], _,
                      %{step: :prepare}}

      refute_receive {:telemetry_event, [:drops, :operation, :step, :start], _,
                      %{step: :validate}}
    end

    test "emits exception events when operation fails" do
      defmodule TestOperationError do
        use Drops.Operations.Command, telemetry: true

        steps do
          @impl true
          def execute(_context) do
            {:error, "something went wrong"}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:error, _reason} = TestOperationError.call(context)

      # Should receive operation start event (first step: prepare)
      assert_receive {:telemetry_event, [:drops, :operation, :start], _,
                      %{step: :prepare}}

      # Should receive exception event for operation stop (last step: execute)
      assert_receive {:telemetry_event, [:drops, :operation, :exception], measurements,
                      metadata}

      assert %{duration: _} = measurements

      assert %{
               operation: TestOperationError,
               step: :execute,
               context: ^context,
               kind: :error,
               reason: "something went wrong"
             } = metadata
    end

    test "does not emit events when telemetry is disabled" do
      defmodule TestOperationDisabled do
        use Drops.Operations.Command

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, Map.put(params, :executed, true)}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:ok, _result} = TestOperationDisabled.call(context)

      # Should not receive any telemetry events
      refute_receive {:telemetry_event, _, _, _}, 100
    end

    test "emits events for multiple configured steps" do
      defmodule TestOperationMultiple do
        use Drops.Operations.Command, telemetry: [steps: [:validate, :execute]]

        steps do
          def prepare(context), do: {:ok, context}

          def validate(context) do
            # Small delay to ensure measurable duration
            Process.sleep(1)
            {:ok, context}
          end

          @impl true
          def execute(%{params: params}) do
            # Small delay to ensure measurable duration
            Process.sleep(1)
            {:ok, Map.put(params, :executed, true)}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:ok, _result} = TestOperationMultiple.call(context)

      # Should receive events for validate step
      assert_receive {:telemetry_event, [:drops, :operation, :step, :start], _,
                      %{step: :validate}}

      assert_receive {:telemetry_event, [:drops, :operation, :step, :stop], measurements,
                      %{step: :validate}}

      assert measurements.duration > 0

      # Should receive events for execute step
      assert_receive {:telemetry_event, [:drops, :operation, :step, :start], _,
                      %{step: :execute}}

      assert_receive {:telemetry_event, [:drops, :operation, :step, :stop], measurements,
                      %{step: :execute}}

      assert measurements.duration > 0

      # Should not receive events for prepare step
      refute_receive {:telemetry_event, [:drops, :operation, :step, :start], _,
                      %{step: :prepare}}
    end
  end

  describe "integration with operation composition" do
    setup do
      # Capture telemetry events
      test_pid = self()

      :telemetry.attach_many(
        "test-composition-telemetry",
        [
          [:drops, :operation, :start],
          [:drops, :operation, :stop]
        ],
        &TelemetryTestHandler.handle_event/4,
        test_pid
      )

      on_exit(fn ->
        :telemetry.detach("test-composition-telemetry")
      end)

      :ok
    end

    test "emits events for composed operations" do
      defmodule FirstOperation do
        use Drops.Operations.Command, telemetry: true

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, Map.put(params, :first_done, true)}
          end
        end
      end

      defmodule SecondOperation do
        use Drops.Operations.Command, telemetry: true

        steps do
          @impl true
          def execute(%{execute_result: result, params: params}) do
            {:ok, Map.merge(result, params)}
          end
        end
      end

      context = %{params: %{name: "test"}}

      result =
        FirstOperation.call(context)
        |> SecondOperation.call(%{params: %{second: true}})

      assert {:ok, %{name: "test", first_done: true, second: true}} = result

      # Should receive events for both operations
      assert_receive {:telemetry_event, [:drops, :operation, :start], _,
                      %{operation: FirstOperation}}

      assert_receive {:telemetry_event, [:drops, :operation, :stop], _,
                      %{operation: FirstOperation}}

      assert_receive {:telemetry_event, [:drops, :operation, :start], _,
                      %{operation: SecondOperation}}

      assert_receive {:telemetry_event, [:drops, :operation, :stop], _,
                      %{operation: SecondOperation}}
    end
  end

  describe "custom identifier configuration" do
    setup do
      # Capture telemetry events with custom identifier
      test_pid = self()

      :telemetry.attach_many(
        "test-custom-identifier-telemetry",
        [
          [:my_app, :operation, :start],
          [:my_app, :operation, :stop],
          [:my_app, :operation, :exception],
          [:my_app, :operation, :step, :start],
          [:my_app, :operation, :step, :stop],
          [:my_app, :operation, :step, :exception]
        ],
        &TelemetryTestHandler.handle_event/4,
        test_pid
      )

      on_exit(fn ->
        :telemetry.detach("test-custom-identifier-telemetry")
      end)

      :ok
    end

    test "emits events with custom identifier for operation boundaries" do
      defmodule TestOperationCustomId do
        use Drops.Operations.Command, telemetry: [identifier: :my_app]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, Map.put(params, :executed, true)}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:ok, _result} = TestOperationCustomId.call(context)

      # Should receive operation start event with custom identifier
      assert_receive {:telemetry_event, [:my_app, :operation, :start], measurements,
                      metadata}

      assert %{system_time: _} = measurements

      assert %{operation: TestOperationCustomId, step: :prepare, context: ^context} =
               metadata

      # Should receive operation stop event with custom identifier
      assert_receive {:telemetry_event, [:my_app, :operation, :stop], measurements,
                      metadata}

      assert %{duration: _} = measurements

      assert %{operation: TestOperationCustomId, step: :execute, context: ^context} =
               metadata
    end

    test "emits events with custom identifier for specific steps" do
      defmodule TestOperationCustomIdSteps do
        use Drops.Operations.Command, telemetry: [identifier: :my_app, steps: [:execute]]

        steps do
          def prepare(context), do: {:ok, context}
          def validate(context), do: {:ok, context}

          @impl true
          def execute(%{params: params}) do
            {:ok, Map.put(params, :executed, true)}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:ok, _result} = TestOperationCustomIdSteps.call(context)

      # Should only receive events for the execute step with custom identifier
      assert_receive {:telemetry_event, [:my_app, :operation, :step, :start],
                      measurements, metadata}

      assert %{system_time: _} = measurements

      assert %{operation: TestOperationCustomIdSteps, step: :execute, context: _} =
               metadata

      assert_receive {:telemetry_event, [:my_app, :operation, :step, :stop], measurements,
                      metadata}

      assert %{duration: _} = measurements

      assert %{operation: TestOperationCustomIdSteps, step: :execute, context: _} =
               metadata

      # Should not receive events for prepare or validate steps
      refute_receive {:telemetry_event, [:my_app, :operation, :step, :start], _,
                      %{step: :prepare}}

      refute_receive {:telemetry_event, [:my_app, :operation, :step, :start], _,
                      %{step: :validate}}
    end

    test "emits exception events with custom identifier" do
      defmodule TestOperationCustomIdError do
        use Drops.Operations.Command, telemetry: [identifier: :my_app]

        steps do
          @impl true
          def execute(_context) do
            {:error, "something went wrong"}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:error, _reason} = TestOperationCustomIdError.call(context)

      # Should receive operation start event with custom identifier
      assert_receive {:telemetry_event, [:my_app, :operation, :start], _,
                      %{step: :prepare}}

      # Should receive exception event with custom identifier
      assert_receive {:telemetry_event, [:my_app, :operation, :exception], measurements,
                      metadata}

      assert %{duration: _} = measurements

      assert %{
               operation: TestOperationCustomIdError,
               step: :execute,
               context: ^context,
               kind: :error,
               reason: "something went wrong"
             } = metadata
    end

    test "does not emit events on default identifier when using custom identifier" do
      defmodule TestOperationNoDefaultEvents do
        use Drops.Operations.Command, telemetry: [identifier: :my_app]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, Map.put(params, :executed, true)}
          end
        end
      end

      context = %{params: %{name: "test"}}
      {:ok, _result} = TestOperationNoDefaultEvents.call(context)

      # Should not receive any events with default :drops identifier
      refute_receive {:telemetry_event, [:drops, :operation, _], _, _}, 100
      refute_receive {:telemetry_event, [:drops, :operation, _, _], _, _}, 100
    end
  end
end
