defmodule Drops.Operations.Extensions.DebugTest do
  use Drops.OperationCase, async: true

  describe "when debug is enabled" do
    operation type: :command, debug: true do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "logging operation start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}
      {:ok, _result} = operation.call(context)

      # TODO: assert that Logger logged debug logs with a message and metadata containing
      # details about the operation, step, and context
    end

    test "logging operation start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}
      {:ok, _result} = operation.call(context)

      # TODO: assert that Logger logged debug logs with a message and metadata containing
      # details about the operation, step, and context
    end
  end
end
