defmodule Drops.Operations.Extensions.DebugTest do
  use Drops.OperationCase, async: true
  import ExUnit.CaptureLog

  describe "when debug is enabled" do
    operation type: :command, debug: true do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "logging operation start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Should log operation start/stop events (first and last steps)
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end

    test "logging operation's step start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Should log all step start/stop events
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step prepare in #{operation}"
      assert log_output =~ "Completed step prepare in"
      assert log_output =~ "Starting step validate in #{operation}"
      assert log_output =~ "Completed step validate in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end
  end

  describe "when debug is disabled" do
    operation type: :command, debug: false do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "does not log debug messages", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Should not contain debug logs from the Debug extension
      refute log_output =~ "Starting step"
      refute log_output =~ "Completed step"
    end
  end

  describe "when debug is enabled with custom identifier" do
    operation type: :command, debug: [identifier: :my_app] do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "logs debug messages with custom identifier", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Should log all step start/stop events
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end
  end
end
