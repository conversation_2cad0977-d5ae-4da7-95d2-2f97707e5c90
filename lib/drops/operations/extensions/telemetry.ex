defmodule Drops.Operations.Extensions.Telemetry do
  @moduledoc """
  Telemetry extension for Operations framework.

  This extension provides telemetry instrumentation for Operations steps,
  allowing you to monitor and observe the execution of your operations.

  ## Features

  - Automatic operation-level telemetry (instruments first and last steps)
  - Configurable step-level instrumentation
  - Integration with Elixir's :telemetry library
  - Metadata includes operation module, step name, and execution context
  - Duration measurements following <PERSON><PERSON>'s telemetry patterns

  ## Usage

  ### Default Behavior

  Enable telemetry with default behavior (instruments operation start/stop using first/last steps):

      defmodule CreateUser do
        use Drops.Operations.Command, telemetry: true

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ### Custom Step Configuration

  Instrument specific steps only:

      defmodule CreateUser do
        use Drops.Operations.Command, telemetry: [steps: [:validate, :execute]]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  Instrument all steps:

      defmodule CreateUser do
        use Drops.Operations.Command, telemetry: [steps: :all]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ### Custom Event Identifier

  Configure a custom identifier for telemetry events (replaces `:drops` in event names):

      defmodule CreateUser do
        use Drops.Operations.Command, telemetry: [identifier: :my_app]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  This will emit events like `[:my_app, :operations, :operation, :start]` instead of `[:drops, :operations, :operation, :start]`.

  ### Combined Configuration

  You can combine custom identifier with step configuration:

      defmodule CreateUser do
        use Drops.Operations.Command, telemetry: [identifier: :my_app, steps: [:validate, :execute]]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ## Telemetry Events

  The extension emits the following telemetry events:

  ### Operation-Level Events (when `telemetry: true`)

  - `[<identifier>, :operation, :start]` - Emitted before the first step executes
  - `[<identifier>, :operation, :stop]` - Emitted after the last step completes successfully
  - `[<identifier>, :operation, :exception]` - Emitted when the last step fails

  ### Step-Level Events (when `telemetry: [steps: [...]]`)

  - `[<identifier>, :operation, :step, :start]` - Emitted before a specific step executes
  - `[<identifier>, :operation, :step, :stop]` - Emitted after a specific step completes successfully
  - `[<identifier>, :operation, :step, :exception]` - Emitted when a specific step fails

  Where `<identifier>` defaults to `:drops` but can be customized using the `:identifier` option.

  ### Event Metadata

  All events include the following metadata:

  - `:operation` - The operation module name
  - `:step` - The actual step name (atom) that was instrumented
  - `:context` - The execution context (map)

  ### Event Measurements

  - `:start` events include `:system_time` (system time when step started)
  - `:stop` events include `:duration` (step execution time in native units)
  - `:exception` events include `:duration` and `:kind`, `:reason`, `:stacktrace`

  ## Example Usage with Telemetry Handlers

      # In your application startup (using default :drops identifier)
      :telemetry.attach_many(
        "operations-telemetry",
        [
          [:drops, :operation, :start],
          [:drops, :operation, :stop],
          [:drops, :operation, :exception],
          [:drops, :operation, :step, :start],
          [:drops, :operation, :step, :stop],
          [:drops, :operation, :step, :exception]
        ],
        &MyApp.TelemetryHandler.handle_event/4,
        %{}
      )

      # Or with custom identifier
      :telemetry.attach_many(
        "my-app-operations-telemetry",
        [
          [:my_app, :operation, :start],
          [:my_app, :operation, :stop],
          [:my_app, :operation, :exception],
          [:my_app, :operation, :step, :start],
          [:my_app, :operation, :step, :stop],
          [:my_app, :operation, :step, :exception]
        ],
        &MyApp.TelemetryHandler.handle_event/4,
        %{}
      )

      defmodule MyApp.TelemetryHandler do
        require Logger

        def handle_event([_identifier, :operation, :start], measurements, metadata, _config) do
          Logger.info("Starting operation \#{metadata.operation} with step \#{metadata.step}")
        end

        def handle_event([_identifier, :operation, :stop], measurements, metadata, _config) do
          duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
          Logger.info("Completed operation \#{metadata.operation} in \#{duration_ms}ms")
        end

        def handle_event([_identifier, :operation, :step, :start], measurements, metadata, _config) do
          Logger.info("Starting step \#{metadata.step} in \#{metadata.operation}")
        end

        def handle_event([_identifier, :operation, :step, :stop], measurements, metadata, _config) do
          duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
          Logger.info("Completed step \#{metadata.step} in \#{duration_ms}ms")
        end

        def handle_event([_identifier, :operation, :exception], measurements, metadata, _config) do
          Logger.error("Failed in \#{metadata.operation}: \#{inspect(metadata.reason)}")
        end

        def handle_event([_identifier, :operation, :step, :exception], measurements, metadata, _config) do
          Logger.error("Failed step \#{metadata.step} in \#{metadata.operation}: \#{inspect(metadata.reason)}")
        end
      end
  """
  use Drops.Operations.Extension

  @impl true
  @spec enable?(keyword()) :: boolean()
  def enable?(opts) do
    # Enable telemetry if explicitly configured or if debug is enabled
    case {Keyword.get(opts, :telemetry, false), Keyword.get(opts, :debug, false)} do
      {false, false} -> false
      # Enable when debug is enabled
      {false, _debug_config} -> true
      {true, _} -> true
      {config, _} when is_list(config) -> true
    end
  end

  @impl true
  @spec default_opts(keyword()) :: keyword()
  def default_opts(_opts) do
    []
  end

  @impl true
  @spec unit_of_work(Drops.Operations.UnitOfWork.t(), keyword()) ::
          Drops.Operations.UnitOfWork.t()
  def unit_of_work(uow, opts) do
    telemetry_config = Keyword.get(opts, :telemetry, false)
    debug_config = Keyword.get(opts, :debug, false)

    # Determine the effective telemetry configuration
    effective_config =
      case {telemetry_config, debug_config} do
        {false, false} ->
          false

        {false, debug_config} ->
          # Debug is enabled but no explicit telemetry config
          # Use the telemetry config that Debug extension would have provided
          debug_identifier =
            case debug_config do
              true -> :drops
              config when is_list(config) -> Keyword.get(config, :identifier, :drops)
              _ -> :drops
            end

          [identifier: debug_identifier, steps: :all]

        {config, _} ->
          # Explicit telemetry configuration takes precedence
          config
      end

    case effective_config do
      false ->
        uow

      true ->
        # Default behavior: instrument first and last steps for operation boundaries
        identifier = :drops
        instrument_operation_boundaries(uow, identifier)

      config when is_list(config) ->
        # Custom configuration with specific steps and/or identifier
        identifier = Keyword.get(config, :identifier, :drops)
        steps_to_instrument = Keyword.get(config, :steps, [])

        cond do
          steps_to_instrument == [] ->
            # No specific steps configured, use operation boundaries
            instrument_operation_boundaries(uow, identifier)

          steps_to_instrument == :all ->
            # Instrument all available steps using step_order
            all_steps = uow.step_order
            instrument_specific_steps(uow, all_steps, identifier)

          true ->
            # Instrument specific steps
            instrument_specific_steps(uow, steps_to_instrument, identifier)
        end
    end
  end

  defp instrument_operation_boundaries(uow, identifier) do
    case uow.step_order do
      [] ->
        uow

      [first_step | _] ->
        last_step = List.last(uow.step_order)

        uow
        # Instrument first step for operation start (using actual step name)
        |> register_before_callback(
          first_step,
          __MODULE__,
          :emit_operation_start,
          {first_step, identifier}
        )
        # Instrument last step for operation stop (using actual step name)
        |> register_after_callback(
          last_step,
          __MODULE__,
          :emit_operation_stop,
          {last_step, identifier}
        )
    end
  end

  defp instrument_specific_steps(uow, step_events, identifier) do
    Enum.reduce(step_events, uow, fn step, acc_uow ->
      # Only instrument if the step exists in the pipeline
      if Map.has_key?(acc_uow.steps, step) do
        acc_uow
        |> register_before_callback(
          step,
          __MODULE__,
          :emit_step_start,
          {step, identifier}
        )
        |> register_after_callback(step, __MODULE__, :emit_step_stop, {step, identifier})
      else
        acc_uow
      end
    end)
  end

  @doc false
  def emit_operation_start(operation_module, _step, context, config) do
    {actual_step, identifier} = config.original_config
    start_time = config.trace.start_time

    :telemetry.execute(
      [identifier, :operation, :start],
      %{system_time: start_time},
      %{operation: operation_module, step: actual_step, context: context}
    )

    :ok
  end

  @doc false
  def emit_operation_stop(operation_module, _step, context, result, config) do
    {actual_step, identifier} = config.original_config
    duration = System.monotonic_time() - config.trace.start_mono

    case result do
      {:ok, _} ->
        :telemetry.execute(
          [identifier, :operation, :stop],
          %{duration: duration},
          %{operation: operation_module, step: actual_step, context: context}
        )

      {:error, reason} ->
        :telemetry.execute(
          [identifier, :operation, :exception],
          %{duration: duration},
          %{
            operation: operation_module,
            step: actual_step,
            context: context,
            kind: :error,
            reason: reason,
            stacktrace: []
          }
        )
    end

    :ok
  end

  @doc false
  def emit_step_start(operation_module, _step, context, config) do
    {actual_step, identifier} = config.original_config

    start_time =
      case Map.get(config.trace.step_timings, actual_step) do
        %{start_time: time} -> time
        _ -> System.system_time()
      end

    :telemetry.execute(
      [identifier, :operation, :step, :start],
      %{system_time: start_time},
      %{operation: operation_module, step: actual_step, context: context}
    )

    :ok
  end

  @doc false
  def emit_step_stop(operation_module, _step, context, result, config) do
    {actual_step, identifier} = config.original_config

    duration =
      case Map.get(config.trace.step_timings, actual_step) do
        %{duration: duration} when not is_nil(duration) -> duration
        %{start_mono: start_mono} -> System.monotonic_time() - start_mono
        _ -> 0
      end

    case result do
      {:ok, _} ->
        :telemetry.execute(
          [identifier, :operation, :step, :stop],
          %{duration: duration},
          %{operation: operation_module, step: actual_step, context: context}
        )

      {:error, reason} ->
        :telemetry.execute(
          [identifier, :operation, :step, :exception],
          %{duration: duration},
          %{
            operation: operation_module,
            step: actual_step,
            context: context,
            kind: :error,
            reason: reason,
            stacktrace: []
          }
        )
    end

    :ok
  end
end
